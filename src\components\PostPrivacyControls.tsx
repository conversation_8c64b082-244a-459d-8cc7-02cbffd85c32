import React, { useState, useRef, useEffect } from 'react';
import { 
  Globe, 
  Users, 
  Lock, 
  UserCheck, 
  Settings, 
  ChevronDown, 
  Eye,
  EyeOff
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

export interface PrivacyOption {
  id: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  value: 'public' | 'friends' | 'friends_except' | 'specific_friends' | 'only_me' | 'custom';
}

export interface CustomAudienceSettings {
  includedFriends: string[];
  excludedFriends: string[];
  includedLists: string[];
  excludedLists: string[];
  allowCommentsFrom: 'everyone' | 'friends' | 'specific';
  allowSharing: boolean;
  hideFromTimeline: boolean;
  preventDownload: boolean;
  expirationDate?: Date;
  locationVisible: boolean;
}

interface Friend {
  id: string;
  name: string;
  avatar: string;
  isOnline?: boolean;
}

interface FriendList {
  id: string;
  name: string;
  memberCount: number;
}

interface PostPrivacyControlsProps {
  currentPrivacy: string;
  onPrivacyChange: (privacy: string, customSettings?: CustomAudienceSettings) => void;
  className?: string;
  showAdvanced?: boolean;
}

const PRIVACY_OPTIONS: PrivacyOption[] = [
  {
    id: 'public',
    label: 'Public',
    description: 'Anyone on or off Facebook',
    icon: <Globe className="w-4 h-4" />,
    value: 'public'
  },
  {
    id: 'friends',
    label: 'Friends',
    description: 'Your friends on Facebook',
    icon: <Users className="w-4 h-4" />,
    value: 'friends'
  },
  {
    id: 'friends_except',
    label: 'Friends except...',
    description: 'Friends except specific people',
    icon: <UserCheck className="w-4 h-4" />,
    value: 'friends_except'
  },
  {
    id: 'specific_friends',
    label: 'Specific friends',
    description: 'Only specific friends',
    icon: <UserCheck className="w-4 h-4" />,
    value: 'specific_friends'
  },
  {
    id: 'only_me',
    label: 'Only me',
    description: 'Only you can see this',
    icon: <Lock className="w-4 h-4" />,
    value: 'only_me'
  },
  {
    id: 'custom',
    label: 'Custom',
    description: 'Advanced privacy settings',
    icon: <Settings className="w-4 h-4" />,
    value: 'custom'
  }
];

const PostPrivacyControls: React.FC<PostPrivacyControlsProps> = ({
  currentPrivacy,
  onPrivacyChange,
  className,
  showAdvanced = true
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isCustomDialogOpen, setIsCustomDialogOpen] = useState(false);
  const [customSettings, setCustomSettings] = useState<CustomAudienceSettings>({
    includedFriends: [],
    excludedFriends: [],
    includedLists: [],
    excludedLists: [],
    allowCommentsFrom: 'friends',
    allowSharing: true,
    hideFromTimeline: false,
    preventDownload: false,
    locationVisible: true
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState<'include' | 'exclude' | 'lists' | 'settings'>('include');

  const dropdownRef = useRef<HTMLDivElement>(null);

  // Mock friends data
  const friends: Friend[] = [
    { id: '1', name: 'Sarah Johnson', avatar: '/placeholder.svg', isOnline: true },
    { id: '2', name: 'Mike Chen', avatar: '/placeholder.svg', isOnline: false },
    { id: '3', name: 'Emma Wilson', avatar: '/placeholder.svg', isOnline: true },
    { id: '4', name: 'David Kim', avatar: '/placeholder.svg', isOnline: false },
    { id: '5', name: 'Lisa Wang', avatar: '/placeholder.svg', isOnline: true }
  ];

  // Mock friend lists
  const friendLists: FriendList[] = [
    { id: '1', name: 'Close Friends', memberCount: 12 },
    { id: '2', name: 'Work', memberCount: 23 },
    { id: '3', name: 'Family', memberCount: 8 },
    { id: '4', name: 'College Friends', memberCount: 45 }
  ];

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const getCurrentPrivacyOption = () => {
    return PRIVACY_OPTIONS.find(option => option.id === currentPrivacy) || PRIVACY_OPTIONS[1];
  };

  const handlePrivacySelect = (privacy: PrivacyOption) => {
    if (privacy.id === 'custom') {
      setIsCustomDialogOpen(true);
    } else {
      onPrivacyChange(privacy.id);
    }
    setIsOpen(false);
  };

  const handleCustomSettingsApply = () => {
    onPrivacyChange('custom', customSettings);
    setIsCustomDialogOpen(false);
  };

  const filteredFriends = friends.filter(friend =>
    friend.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const toggleFriendInList = (friendId: string, listType: 'included' | 'excluded') => {
    setCustomSettings(prev => {
      const key = listType === 'included' ? 'includedFriends' : 'excludedFriends';
      const list = prev[key];
      const isIncluded = list.includes(friendId);
      
      return {
        ...prev,
        [key]: isIncluded 
          ? list.filter(id => id !== friendId)
          : [...list, friendId]
      };
    });
  };

  const toggleFriendList = (listId: string, listType: 'included' | 'excluded') => {
    setCustomSettings(prev => {
      const key = listType === 'included' ? 'includedLists' : 'excludedLists';
      const list = prev[key];
      const isIncluded = list.includes(listId);
      
      return {
        ...prev,
        [key]: isIncluded 
          ? list.filter(id => id !== listId)
          : [...list, listId]
      };
    });
  };

  const currentOption = getCurrentPrivacyOption();

  return (
    <div className={cn('relative', className)}>
      {/* Privacy Selector Button */}
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 h-9 px-3"
      >
        {currentOption.icon}
        <span className="text-sm font-medium">{currentOption.label}</span>
        <ChevronDown className="w-4 h-4" />
      </Button>

      {/* Privacy Options Dropdown */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            ref={dropdownRef}
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            className="absolute top-full left-0 mt-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50"
          >
            <div className="p-4">
              <h3 className="font-semibold text-sm text-gray-900 dark:text-white mb-3">
                Who can see your post?
              </h3>
              
              <div className="space-y-1">
                {PRIVACY_OPTIONS.map((option) => (
                  <button
                    key={option.id}
                    onClick={() => handlePrivacySelect(option)}
                    className={cn(
                      'w-full flex items-center space-x-3 p-3 rounded-lg text-left transition-colors',
                      currentPrivacy === option.id 
                        ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' 
                        : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                    )}
                  >
                    <div className="text-gray-600 dark:text-gray-400">{option.icon}</div>
                    <div className="flex-1">
                      <div className="font-medium text-sm text-gray-900 dark:text-white">
                        {option.label}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {option.description}
                      </div>
                    </div>
                    {currentPrivacy === option.id && (
                      <div className="w-2 h-2 bg-blue-600 rounded-full" />
                    )}
                  </button>
                ))}
              </div>

              {showAdvanced && (
                <>
                  <Separator className="my-3" />
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Your privacy settings affect who can see your post and interact with it.
                  </div>
                </>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Custom Privacy Settings Dialog */}
      <Dialog open={isCustomDialogOpen} onOpenChange={setIsCustomDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Settings className="w-5 h-5" />
              <span>Custom Privacy Settings</span>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Tab Navigation */}
            <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
              {[
                { id: 'include', label: 'Include', icon: <Eye className="w-4 h-4" /> },
                { id: 'exclude', label: 'Exclude', icon: <EyeOff className="w-4 h-4" /> },
                { id: 'lists', label: 'Friend Lists', icon: <Users className="w-4 h-4" /> },
                { id: 'settings', label: 'Settings', icon: <Settings className="w-4 h-4" /> }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as 'audience' | 'timeline' | 'tagging' | 'location' | 'advanced')}
                  className={cn(
                    'flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors flex-1 justify-center',
                    activeTab === tab.id
                      ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  )}
                >
                  {tab.icon}
                  <span>{tab.label}</span>
                </button>
              ))}
            </div>

            {/* Tab Content */}
            <div className="min-h-[300px]">
              {(activeTab === 'include' || activeTab === 'exclude') && (
                <div className="space-y-4">
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white mb-3">
                      {activeTab === 'include' ? 'Include specific friends' : 'Exclude specific friends'}
                    </h3>
                    <Input
                      placeholder="Search friends..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="mb-3"
                    />
                  </div>

                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {filteredFriends.map((friend) => {
                      const isSelected = activeTab === 'include' 
                        ? customSettings.includedFriends.includes(friend.id)
                        : customSettings.excludedFriends.includes(friend.id);

                      return (
                        <div
                          key={friend.id}
                          className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
                        >
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={() => toggleFriendInList(
                              friend.id, 
                              activeTab === 'include' ? 'included' : 'excluded'
                            )}
                          />
                          <Avatar className="w-8 h-8">
                            <AvatarImage src={friend.avatar} />
                            <AvatarFallback>{friend.name[0]}</AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <div className="font-medium text-sm">{friend.name}</div>
                            {friend.isOnline && (
                              <Badge variant="secondary" className="text-xs">Online</Badge>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {activeTab === 'lists' && (
                <div className="space-y-4">
                  <h3 className="font-medium text-gray-900 dark:text-white">
                    Friend Lists
                  </h3>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Include Lists
                      </h4>
                      <div className="space-y-2">
                        {friendLists.map((list) => (
                          <div
                            key={list.id}
                            className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
                          >
                            <Checkbox
                              checked={customSettings.includedLists.includes(list.id)}
                              onCheckedChange={() => toggleFriendList(list.id, 'included')}
                            />
                            <div className="flex-1">
                              <div className="font-medium text-sm">{list.name}</div>
                              <div className="text-xs text-gray-500">
                                {list.memberCount} members
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Exclude Lists
                      </h4>
                      <div className="space-y-2">
                        {friendLists.map((list) => (
                          <div
                            key={list.id}
                            className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
                          >
                            <Checkbox
                              checked={customSettings.excludedLists.includes(list.id)}
                              onCheckedChange={() => toggleFriendList(list.id, 'excluded')}
                            />
                            <div className="flex-1">
                              <div className="font-medium text-sm">{list.name}</div>
                              <div className="text-xs text-gray-500">
                                {list.memberCount} members
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'settings' && (
                <div className="space-y-4">
                  <h3 className="font-medium text-gray-900 dark:text-white">
                    Additional Settings
                  </h3>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">Allow sharing</Label>
                        <p className="text-xs text-gray-500">Let others share your post</p>
                      </div>
                      <Checkbox
                        checked={customSettings.allowSharing}
                        onCheckedChange={(checked) => 
                          setCustomSettings(prev => ({ ...prev, allowSharing: checked as boolean }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">Hide from timeline</Label>
                        <p className="text-xs text-gray-500">Don't show on your timeline</p>
                      </div>
                      <Checkbox
                        checked={customSettings.hideFromTimeline}
                        onCheckedChange={(checked) => 
                          setCustomSettings(prev => ({ ...prev, hideFromTimeline: checked as boolean }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">Show location</Label>
                        <p className="text-xs text-gray-500">Show location with this post</p>
                      </div>
                      <Checkbox
                        checked={customSettings.locationVisible}
                        onCheckedChange={(checked) => 
                          setCustomSettings(prev => ({ ...prev, locationVisible: checked as boolean }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">Prevent downloads</Label>
                        <p className="text-xs text-gray-500">Prevent others from downloading images</p>
                      </div>
                      <Checkbox
                        checked={customSettings.preventDownload}
                        onCheckedChange={(checked) => 
                          setCustomSettings(prev => ({ ...prev, preventDownload: checked as boolean }))
                        }
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
              <Button
                variant="outline"
                onClick={() => setIsCustomDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleCustomSettingsApply}>
                Apply Settings
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PostPrivacyControls;
